#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试main.bat功能的简单脚本
"""

import sys
import os

def test_environment():
    """测试环境信息"""
    print("🧪 测试环境信息:")
    print(f"Python版本: {sys.version}")
    print(f"Python路径: {sys.executable}")
    print(f"当前工作目录: {os.getcwd()}")
    
    # 检查虚拟环境
    if hasattr(sys, 'real_prefix') or (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix):
        print("✅ 运行在虚拟环境中")
        print(f"虚拟环境路径: {sys.prefix}")
    else:
        print("⚠️  运行在系统Python环境中")
    
    # 检查PaddleOCR
    try:
        import paddleocr
        print(f"✅ PaddleOCR版本: {paddleocr.__version__}")
    except ImportError:
        print("❌ PaddleOCR未安装")
    
    # 检查其他依赖
    dependencies = ['pandas', 'openpyxl', 'PIL']
    for dep in dependencies:
        try:
            __import__(dep)
            print(f"✅ {dep}: 已安装")
        except ImportError:
            print(f"❌ {dep}: 未安装")
    
    print("\n🎉 环境测试完成！")
    print("如果看到这条消息，说明main.bat工作正常！")
    
    input("\n按回车键退出测试...")

if __name__ == "__main__":
    test_environment()
