@echo off
:: 设置控制台编码为GBK，防止中文乱码
chcp 936 >nul 2>&1
:: 设置环境变量确保正确显示中文
set PYTHONIOENCODING=utf-8
title 测试启动器 ceshi

echo.
echo =============== ceshi 启动器 ===============
echo.

:: 切换到脚本所在目录
cd /d "%~dp0"

echo Python已检测到，准备运行主程序...


:: 检查并激活虚拟环境
set "VENV_PATH=C:\Users\<USER>\lanhai"
set "PYTHON_EXE=%VENV_PATH%\Scripts\python.exe"
if exist "%PYTHON_EXE%" (
    echo 检测到虚拟环境，正在激活...
    call "%VENV_PATH%\Scripts\activate.bat"
    echo 使用虚拟环境的 Python 运行主程序...
    "%PYTHON_EXE%" main.py
) else (
    echo 未检测到虚拟环境 C:\Users\<USER>\lanhai\ ，使用系统Python运行。
    python --version >nul 2>&1
    if %errorlevel% neq 0 (
        echo 未检测到Python，请先安装Python！
        pause
        exit /b
    )
    python main.py
)

echo.
echo =============== 运行结束 ===============
pause
