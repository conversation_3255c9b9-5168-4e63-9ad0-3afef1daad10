o

    �h�!  �                   @   sN   d Z ddlZddlZddlZddlmZmZmZ ddl	m
Z
 G dd� d�ZdS )uE   
数据导出模块
负责将分析结果导出为Excel和JSON格式
�    N)�List�Dict�Any)�
messageboxc                   @   s�   e Zd ZdZdd� Zdeeeef  dede	fdd�Z
deeeef  dede	fd	d
�Zdeeeef  defdd�Zdeeeef  deeef fd
d�Z
dS )�DataExporteru   数据导出器c                 C   s   t �t�| _d S )N)�logging�	getLogger�__name__�logger)�self� r   �IF:\zuomianwenjian\3.10.11\1213111\douyin_guaji-main\1111\data_exporter.py�__init__   s   zDataExporter.__init__�results�filename�returnc                 C   sZ  �z|s
t �dd� W dS g }|D ]n}|�dd�|�dd�|�dd�|�d	d�|�d
d�|�dd�|�d
d�|�dd�|�dd�|�dd�|�dd�|�dd�|�dd�|�dd�d�}d|v rf|d |d< d|v rzt|d t�rzd�|d �|d< |�|� qt�|�}|j	ddd�}tj
|dd��V}|j|ddd� |jd }i dd�d d!�d"d#�d$d%�d&d!�d'd#�d(d!�d)d#�d*d!�d+d#�d,d#�d-d!�d.d#�d/d#�d0d�d1d2�}	|	�
� D ]
\}
}||j|
 _q�W d3  � n1 s�w   Y  t �d4d5|� �� | j�d6|� �� W d7S  t�y, } zd8t|�� �}
t �d9|
� | j�|
� W Y d3}~dS d3}~ww ):u   导出Excel文件�   警告�   没有分析结果可导出！F�   产品名称� �	   文件名�   蓝海指数r   �   评级�   搜索人气�   搜索人气增长�-�   支付转化率�   转化率增长�   支付买家数�   买家数增长�   需求供给比�   需求供给比增长�   天猫占比�   天猫占比增长)r   r   r   r   r   r   r   r   r   r   r    r!   r"   r#   �   否决原因u   分析详情z; )Z	ascendingZopenpyxl)Zengineu   蓝海分析结果)Z
sheet_name�index�A�   �B�   �C�   �D�   �E�F�G�H�I�J�K�L�M�N�O�P�2   N�   导出成功u   Excel文件已保存到: u   Excel导出成功: Tu   导出Excel失败: �   导出失败)r   �showwarning�get�
isinstance�list�join�append�pdZ	DataFrameZsort_valuesZExcelWriterZto_excelZsheets�itemsZcolumn_dimensions�width�showinfor
   �info�	Exception�str�	showerror�error)r   r   r   �export_data�result�rowZdf�writerZ	worksheetZ
column_widths�colrE   �e�	error_msgr   r   r
   �export_to_excel   s�   













�

��������	�
���
��������zDataExporter.export_to_excelc              
   C   s  zZ|st �dd� W dS t|dd� dd�}t|�tj�� �d�d	d
�|d�}t|dd
d��}t	j
||ddd� W d  � n1 sAw   Y  t �dd|� �� | j�
d|� �� W dS  ty� } zdt|�� �}t �d|� | j�|� W Y d}~dS d}~ww )u   导出JSON文件r   r   Fc                 S   �   | � dd�S �Nr   r   �r>   ��xr   r   r
   �<lambda>o   �    z-DataExporter.export_to_json.<locals>.<lambda>T)�key�reversez%Y-%m-%d %H:%M:%Sz1.0)Ztotal_productsZexport_time�version)Zexport_infor   �wzutf-8)�encoding�   )�ensure_ascii�indentNr;   u   JSON文件已保存到: u   JSON导出成功: u   导出JSON失败: r<   )r   r=   �sorted�lenrC   Z	Timestamp�now�strftime�open�json�dumprF   r
   rG   rH   rI   rJ   rK   )r   r   r   �sorted_resultsrL   �frQ   rR   r   r   r
   �export_to_jsong   s0   ��
���zDataExporter.export_to_jsonc              	   C   s0  |sdS t |�}dd� |D �}t |�}dd� |D �}t |�}|r<tdd� |D ��t |� }t|dd� d	�}|�d
d�}	nd}dd
i}d}	i }
|D ]}|�dd�}|
�|d�d |
|< qHd|� d|� d|� d|d�d�	}
|
�� D ]\}}|| d }|
d|� d|� d|d�d�7 }
qo|
d|d � d|	� d�7 }
|
S )u   生成分析摘要报告u   没有分析结果c                 S   �   g | ]}d |v r|�qS �r$   r   ��.0�rr   r   r
   �
<listcomp>�   �    z8DataExporter.generate_summary_report.<locals>.<listcomp>c                 S   s   g | ]}d |vr|�qS rn   r   ro   r   r   r
   rr   �   rs   c                 s   s   � | ]	}|� d d�V  qdS )r   r   NrV   ro   r   r   r
   �	<genexpr>�   s   � z7DataExporter.generate_summary_report.<locals>.<genexpr>c                 S   rT   rU   rV   rW   r   r   r
   rY   �   rZ   z6DataExporter.generate_summary_report.<locals>.<lambda>)r[   r   r   r   u   无r   �   未知�   u3   📊 蓝海产品分析摘要报告

总产品数: u   
🚫 一票否决: u   个
✅ 通过筛选: u   个
平均蓝海指数: z.1fu7    (仅统计通过筛选的产品)

产品评级分布:
�d   u   • z: u   个 (z%)
u   
🏆 最佳产品: u   
📈 最高指数: u;   

💡 左侧显示识别顺序，右侧显示评分排序
)rd   �sum�maxr>   rD   )r   r   �total_countZvetoed_results�vetoed_countZpassed_results�passed_count�	avg_scoreZbest_product�	max_scoreZratingsrM   �rating�summary�countZ
percentager   r   r
   �generate_summary_report�   sH   ���
�
 �
�z$DataExporter.generate_summary_reportc           
      C   s�   |si S t |�}t dd� |D ��}|| }i }|D ]}|�dd�}|�|d�d ||< qdd� |D �}|r9t|�nd|r@t|�nd|rKt|�t |� ndd�}	|||||	d	�S )
u   获取导出统计信息c                 S   rm   rn   r   ro   r   r   r
   rr   �   rs   z6DataExporter.get_export_statistics.<locals>.<listcomp>r   ru   r   rv   c                 S   s    g | ]}d |vr|� dd��qS )r$   r   r   rV   ro   r   r   r
   rr   �   s     )r~   Z	min_scorer}   )rz   r{   r|   Zrating_distributionZscore_statistics)rd   r>   ry   �minrx   )
r   r   rz   r{   r|   Zrating_statsrM   r   ZscoresZscore_statsr   r   r
   �get_export_statistics�   s(   ��z"DataExporter.get_export_statisticsN)r	   �
__module__�__qualname__�__doc__r   r   r   rI   r   �boolrS   rl   r�   r�   r   r   r   r
   r      s    ""S"*8r   )r�   rh   r   ZpandasrC   �typingr   r   r   �tkinterr   r   r   r   r   r
   �<module>   s   