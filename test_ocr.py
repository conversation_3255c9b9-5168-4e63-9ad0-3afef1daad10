#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试OCR处理脚本
"""

import os
import sys
from config_manager import ConfigManager
from ocr_processor import OCRProcessor

def test_ocr():
    """测试OCR处理"""
    print("🚀 测试OCR处理...")
    
    # 初始化配置管理器
    config_manager = ConfigManager()
    
    # 初始化OCR处理器
    ocr_processor = OCRProcessor(config_manager)
    
    # 测试图片路径 - 使用原始的测试图片
    image_path = "F:/zuomianwenjian/3.10.11/1213111/douyin_guaji-main/1111/33/Screenshot_20250724_111304.png"
    
    if not os.path.exists(image_path):
        print(f"❌ 图片文件不存在: {image_path}")
        return
    
    print(f"📸 处理图片: {image_path}")
    
    # 处理图片
    try:
        products_data = ocr_processor.process_image(image_path)
        
        print(f"\n🎉 识别结果:")
        print(f"识别到 {len(products_data)} 个产品")
        
        for i, product in enumerate(products_data, 1):
            print(f"\n产品 {i}:")
            for key, value in product.items():
                print(f"  {key}: {value}")
                
    except Exception as e:
        print(f"❌ 处理失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_ocr()
