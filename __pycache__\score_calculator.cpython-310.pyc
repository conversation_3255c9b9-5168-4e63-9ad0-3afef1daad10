o

    ��h�3  �                   @   s>   d Z ddlZddlZddlmZmZmZmZ G dd� d�ZdS )uE   
评分计算模块
负责蓝海指数计算和一票否决权检查
�    N)�Dict�Any�Tuple�Listc                   @   s�   e Zd ZdZdd� Zdedefdd�Zdedefd	d
�Z	dedefdd
�Z
dedefdd�Zdededefdd�Zde
eef deeee f fdd�Zde
eef de
eef fdd�ZdS )�ScoreCalculatoru   评分计算器c                 C   s   || _ t�t�| _d S �N)�config_manager�logging�	getLogger�__name__�logger)�selfr   � r   �LF:\zuomianwenjian\3.10.11\1213111\douyin_guaji-main\1111\score_calculator.py�__init__   s   zScoreCalculator.__init__�	range_str�returnc                 C   s@   |r|dkrdS t |��dd�}t�d|�}|rt|d �S dS )u$   从范围字符串中提取最小值�-r   u   万Z0000�\d+)�str�replace�re�findall�int)r
   r   �numbersr   r   r   �extract_min_from_range   s   z&ScoreCalculator.extract_min_from_range�percent_strc                 C   s<   t |t�r|�dd�}t�d|�}|rtdd� |D ��S dS )u$   从百分比字符串中提取数值�%� �(\d+\.?\d*)c                 s   �   � | ]}t |�V  qd S r   )�float��.0�numr   r   r   �	<genexpr>)   �   � z5ScoreCalculator.extract_percentage.<locals>.<genexpr>�        )�
isinstancer   r   r   r   �max)r
   r   �	clean_strr   r   r   r   �extract_percentage!   s   
z"ScoreCalculator.extract_percentage�rate_strc                 C   s0   t |t�rt�d|�}|rtdd� |D ��S dS )u'   从转化率字符串中提取最大值r   c                 s   r    r   )r   r"   r   r   r   r%   1   r&   z3ScoreCalculator.extract_max_rate.<locals>.<genexpr>r   )r(   r   r   r   r)   )r
   r,   r   r   r   r   �extract_max_rate,   s
   
z ScoreCalculator.extract_max_rate�
growth_strc                 C   sP   |r|dkrdS t |��dd��dd��dd�}t�d|�}|r&t|d �S dS )	u$   从增长率字符串中提取数值r   r'   �+r   r   �,r   r   )r   r   r   r   r!   )r
   r.   r*   r   r   r   r   �extract_growth_rate4   s    z#ScoreCalculator.extract_growth_rate�key�
default_valuec                 C   s   | j �� }|�||�S )u   获取当前配置值)r   �get_filter_config�get)r
   r2   r3   �
filter_configr   r   r   �get_current_config_valueB   s   
z(ScoreCalculator.get_current_config_value�product_datac                 C   s  g }| � dd�}| � dd�}| � dd�}| � dd�}|�d	d
�}| �|�}||k r5|�d|� d|� d
�� |�dd
�}	| �|	�}
|
|k rP|�d|
� d|� d
�� |�dd�}||k rf|�d|� d|� d
�� |�dd
�}| �|�}
|
|kr�|�d|
� d|� d�� t|�dk}||fS )uW   
        检查一票否决权条件
        返回: (is_vetoed, veto_reasons)
        u   否决_搜索人气最小值i�  u   否决_支付买家数最小值�
   u   否决_需求供给比最小值g      �?u   否决_天猫占比最大值g     �Q@�   搜索人气r   u   搜索人气过低(z < �)u   支付买家数u   支付买家数过低(�   需求供给比r   u   需求供给比过低(�   天猫占比u   天猫占比过高(z% > z%))r7   r5   r   �appendr+   �len)r
   r8   �veto_reasonsZmin_popularity_thresholdZmin_buyers_thresholdZmin_supply_demand_thresholdZmax_tmall_threshold�search_popularityZmin_popularityZbuyer_countZ
min_buyers�supply_demand_ratio�tmall_ratio�tmall_percentage�	is_vetoedr   r   r   �check_veto_conditionsG   s,   


z%ScoreCalculator.check_veto_conditionsc                 C   s.  | � |�\}}|ri |�ddd�|�dd� |D �d��S d}dg}| j�� }| j�� }|�dd�}|d	 d }	||�d
d�krT||	d 7 }|�d
|� d|	d � d�� nA||�dd�krq||	d 7 }|�d|� d|	d � d�� n$||	d kr�||	d 7 }|�d|� d|	d � d�� n	|�d|� d�� | �|�dd��}
|d	 d }|
|�dd�kr�||d 7 }|�d|
� d|d � d�� nA|
|�d d!�kr�||d 7 }|�d"|
� d|d � d�� n$|
|d kr�||d 7 }|�d#|
� d|d � d�� n	|�d$|
� d%�� | �|�d&d'��}|d	 d& }
||�d(d)�k�r/||
d* 7 }|�d+|� d|
d* � d�� n2||�d,d-�k�rM||
d. 7 }|�d/|� d|
d. � d�� n||
d0 7 }|�d1|� d|
d0 � d�� | �	|�d2d3��}|d	 d2 }||d4 k�r�||d5 7 }|�d6|� d|d5 � d�� nL||d7 k�r�||d8 7 }|�d9|� d|d8 � d�� n0||d: k�r�||d; 7 }|�d<|� d|d; � d�� n||d= 7 }|�d>|� d|d= � d�� d}|d? }| �
|�d@d��}|d@ }||dA k�r||dB 7 }|�dC|� d|dB � d�� n7||dD k�r#||dE 7 }|�dF|� d|dE � d�� n||dG k�r>||dH 7 }|�dI|� d|dH � d�� | �
|�dJd��}|dJ }||dA k�rg||dB 7 }|�dK|� d|dB � d�� n7||dG k�r�||dH 7 }|�dL|� d|dH � d�� n||dM k�r�||dN 7 }|�dO|� d|dN � d�� | �
|�dPd��}|dP }||dA k�r�||dB 7 }|�dQ|� d|dB � d�� n||dG k�r�||dH 7 }|�dR|� d|dH � d�� t|| dS�}|dT }||dU k�r�dV}n||dW k�rdX}n||dY k�rdZ}nd[}i |�|||d\��S )]u0   计算蓝海指数 - 添加一票否决权功能r   u   🚫 红海产品(一票否决)z; c                 S   s   g | ]}d |� ��qS )u   ❌ r   )r#   �reasonr   r   r   �
<listcomp>y   s    z>ScoreCalculator.calculate_blue_ocean_score.<locals>.<listcomp>)�   蓝海指数�   评级u   否决原因�   分析详情u   ✅ 通过一票否决权检查r<   u   基础评分u   需求供给比_优秀g      @u
   优秀_分数u   🔥 需求供给比优秀: z (+u   分)u   需求供给比_良好g       @u
   良好_分数u   👍 需求供给比良好: u
   一般_阈值u
   一般_分数u   ⚠️ 需求供给比一般: u   ❌ 需求供给比较低: u    (+0分)u   支付转化率z0%u	   转化率u   转化率_优秀g      D@u   💰 转化率优秀: z% (+u   转化率_良好g      9@u   💵 转化率良好: u   💸 转化率一般: u   ❌ 转化率较低: u	   % (+0分)r=   z100%u   天猫占比_低g      I@u
   低_分数u   🎯 天猫占比低: u   天猫占比_中g      T@u
   中_分数u   ⚖️ 天猫占比中等: u
   高_分数u   ⚠️ 天猫占比较高: r:   �0u
   很高_阈值u
   很高_分数u   🔥 搜索人气很高: u
   较高_阈值u
   较高_分数u   📈 搜索人气较高: u
   中等_阈值u
   中等_分数u   📊 搜索人气中等: u
   较低_分数u   📉 搜索人气较低: u   趋势加分u   需求供给比增长u
   暴增_阈值u
   暴增_分数u   🚀 需求供给比暴增u
   大增_阈值u
   大增_分数u   📈 需求供给比大增u
   增长_阈值u
   增长_分数u   📊 需求供给比增长u   搜索人气增长u   🔥 搜索人气暴增u   📈 搜索人气增长u   小幅增长_阈值u   小幅增长_分数u   📊 搜索人气小幅增长u   买家数增长u   👥 买家数暴增u   👥 买家数增长�d   u   评级标准u   蓝海产品_最低分u   🥇 蓝海产品u   潜力产品_最低分u   🥈 潜力产品u   一般产品_最低分u   🥉 一般产品u   🔴 红海产品)rI   rJ   rK   )rF   �joinr   r4   �get_scoring_configr5   r>   r-   r+   r   r1   �min)r
   r8   rE   r@   Z
base_scoreZanalysis_detailsr6   �scoring_configrB   Z
supply_config�conversion_rateZconversion_configrD   Ztmall_configrA   Zpopularity_configZtrend_bonusZtrend_configZ
supply_growthZsupply_growth_configZpopularity_growthZpopularity_growth_configZbuyer_growthZbuyer_growth_configZfinal_scoreZ
rating_configZratingr   r   r   �calculate_blue_ocean_scoren   s�   ��	

��z*ScoreCalculator.calculate_blue_ocean_scoreN)r   �
__module__�__qualname__�__doc__r   r   r   r   r!   r+   r-   r1   r7   r   r   r   �boolr   rF   rS   r   r   r   r   r      s    &&'r   )	rV   r   r	   �typingr   r   r   r   r   r   r   r   r   �<module>   s
   