o

    �h(S  �                   @   sZ   d Z ddlZddlZddlZddlZddlmZmZm	Z	m
Z
 ddlmZ G dd� d�Z
dS )ui   
数据导入分析模块
支持导入Excel和JSON文件，重新分析历史数据，支持批量导入
�    N)�List�Dict�Any�Optional)�
messageboxc                
   @   s�  e Zd ZdZdd� Zdedeeeee	f   fdd�Z
dedeeeee	f   fdd	�Zd
eeee	f  deeee	f  fdd�Zd
ee deeeee	f   fdd�Z
deee	f fdd�Z	d%d
eeee	f  dedeeee	f  fdd�Z	d&d
eeee	f  dedeeee	f  fdd�Zd
eeee	f  defdd�Zdeeee	f  deeee	f  deee	f fdd �Zd!eee	f defd"d#�Zd$S )'�DataImporteru   数据导入器c                 C   s   || _ || _t�t�| _d S )N)�config_manager�score_calculator�logging�	getLogger�__name__�logger)�selfr   r	   � r   �IF:\zuomianwenjian\3.10.11\1213111\douyin_guaji-main\1111\data_importer.py�__init__   s   zDataImporter.__init__�	file_path�returnc           
   
   C   s�   zG| j �d|� �� t�|�}|�d�}g }|D ] }i }|�� D ]\}}t�|�r.|||< q d||< q |�|� q| j �dt|�� d�� |W S  t	yl }	 z| j �
d|	� �� t�dd|	� �� W Y d	}	~	d	S d	}	~	ww )
u   导入Excel文件u   开始导入Excel文件: �records� �
   成功导入 �
    条数据u   导入Excel文件失败: �   导入失败u   无法导入Excel文件:
N)
r
   �info�pd�
read_excel�to_dict�items�notna�append�len�	Exception�errorr   �	showerror)
r   r   �df�	data_listZcleaned_data�itemZcleaned_item�key�value�er   r   r   �import_excel_file   s(   




��zDataImporter.import_excel_filec              
   C   s  z[| j �d|� �� t|ddd��
}t�|�}W d  � n1 s!w   Y  t|t�rAd|v r4|d }nd|v r=|d }n|g}nt|t�rI|}ntd��| j �d	t	|�� d
�� |W S  t
y� } z| j �d|� �� t�
dd
|� �� W Y d}~dS d}~ww )u   导入JSON文件u   开始导入JSON文件: �rzutf-8)�encodingN�results�datau   不支持的JSON数据格式r   r   u   导入JSON文件失败: r   u   无法导入JSON文件:
)r
   r   �open�json�load�
isinstance�dict�list�
ValueErrorr    r!   r"   r   r#   )r   r   �fr.   r%   r)   r   r   r   �import_json_file6   s,   �



��zDataImporter.import_json_filer%   c           	      C   s�  z�| j �dt|�� d�� g }d}t|�D ]i\}}z&| j�|�}|�|� |d d dkr>| j �d|d � dt|�� d�� W q ty~ } z3| j �d|d � d	|� �� |�	� }d|d
< d|d< d
t
|�� �g|d< |�|� |d7 }W Y d}~qd}~ww | j �dt|�| � d|� d�� |dkr�t�dd|� d�� |W S  ty� } z| j �
d|� �� t�dd|� �� g W  Y d}~S d}~ww )u   重新分析导入的数据u   开始重新分析 r   r   �   �
   u
   已处理 �/u   重新分析第 u    条数据失败: �   蓝海指数u   ❌ 分析失败�   评级u   重新分析失败: u   分析详情Nu   重新分析完成: 成功 u    条，失败 �    条u   部分失败u   重新分析完成，但有 u    条数据分析失败u   重新分析数据失败: u   分析失败u   重新分析数据时出错:
)r
   r   r    �	enumerater	   �calculate_blue_ocean_scorer   r!   �warning�copy�strr   �showwarningr"   r#   )	r   r%   Zreanalyzed_resultsZfailed_count�ir&   �resultr)   �failed_itemr   r   r   �reanalyze_dataU   s>   
"�
��"
��zDataImporter.reanalyze_data�
file_pathsc           	      C   s�  �zC| j �dt|�� d�� g }t|�dddg d�}t|�D �]\}}z�| j �d|d � dt|�� dtj�|�� �� |�� �d	�rH| �	|�}n0|�� �d
�rU| �
|�}n#| j �d|� �� |d  d7  < |d
 �tj�|�dddd�� W q|r�|D ]}tj�|�|d< t
j�� �d�|d< q||�|� |d  d7  < |d  t|�7  < |d
 �tj�|�ddt|�d�� | j �dtj�|�� dt|�� d�� n|d  d7  < |d
 �tj�|�dddd�� W q t�y  } z,| j �d|� d|� �� |d  d7  < |d
 �tj�|�dt|�dd�� W Y d}~qd}~ww | j �d|d � d|d � d |d � d�� | �|� |�rB|W S dW S  t�yj } z| j �d!|� �� t�d"d#|� �� W Y d}~dS d}~ww )$u   批量导入多个文件u   开始批量导入 u
    个文件r   )�total_files�
success_files�failed_files�
total_records�file_detailsu
   导入文件 r8   r:   �: )z.xlsxz.xlsz.jsonu   不支持的文件格式: rK   rM   u   失败u   不支持的文件格式)�file�status�reasonr   �   数据来源�%Y-%m-%d %H:%M:%S�   导入时间rJ   rL   �   成功r   u   文件 u    导入成功: �
    条记录u   文件读取失败u	    失败: Nu   批量导入完成: 成功 u    个文件，失败 u    个文件，总计 u   批量导入失败: u   批量导入失败u   批量导入文件时出错:
)r
   r   r    r>   �os�path�basename�lower�endswithr*   r7   r@   r   r   �	Timestamp�now�strftime�extendr!   r"   rB   �show_import_summaryr   r#   )	r   rH   Zall_dataZimport_summaryrD   r   Z	file_datar&   r)   r   r   r   �batch_import_files}   s�   �.
�

�(
��
���
�
�
��zDataImporter.batch_import_files�summaryc              	   C   s�   d|d � d|d � d|d � d|d � d	�	}|d
 D ]/}|d dkr%d
nd}||� d|d � d|d � d�7 }|d rF|d|d � d�7 }|d7 }qt �d|� dS )u   显示导入摘要u'   📊 批量导入摘要

总文件数: rI   u   
成功导入: rJ   u    个文件
导入失败: rK   u    个文件
总记录数: rL   u    条

📋 文件详情:
rM   rP   rU   u   ✅u   ❌� rO   rN   r   rV   rQ   � (�)�
u   批量导入完成N)r   �showinfo)r   rb   Zsummary_text�detailZstatus_iconr   r   r   r`   �   s    ����
"
z DataImporter.show_import_summary�   产品名称�
dedupe_keyc              
   C   sb  z�| j �dt|�� d�� i }d}|D ]G}|�|dt|�� ��}||v rV|| �dd�}|�dd�}||krH|||< |d7 }| j �d|� �� q|d7 }| j �d	|� �� q|||< qt|�� �}	| j �d
t|�� dt|	�� d|� d
�� |dkr�t�dd|� dt|	�� d
�� |	W S  t	y� }
 z| j �
d|
� �� t�dd|
� �� |W  Y d}
~
S d}
~
ww )u   合并数据并去重u   开始合并和去重 r   r   u
   未知产品_rT   z1900-01-01 00:00:00r8   u   更新重复产品: u   跳过旧数据: u   合并完成: 原始 u    条，去重后 u    条，重复 r=   u   数据去重u   发现 u;    条重复数据
已自动保留最新版本
最终数据: u   合并数据失败: u   合并失败u   合并数据时出错:
N)r
   r   r    �get�debugr4   �valuesr   rg   r!   r"   r#   )r   r%   rj   �merged_dataZduplicate_countr&   r'   Z
existing_timeZcurrent_timerE   r)   r   r   r   �merge_and_deduplicate_data�   sB   

�����z'DataImporter.merge_and_deduplicate_dataF�	ascendingc              
   C   s�   zJ| j �dt|�� d�� t|dd� | d�}t|�D ]\}}|d |d< tj�� �d�|d	< q| j �d
|d �	dd�� d
|d �	dd�� �� |W S  t
yp } z| j �d|� �� t�
dd|� �� |W  Y d}~S d}~ww )u   按蓝海指数排序u   按蓝海指数排序 r   c                 S   s   | � dd�S )Nr;   r   �rk   ��xr   r   r   �<lambda>  s    z7DataImporter.sort_by_blue_ocean_score.<locals>.<lambda>�r'   �reverser8   u   排名rS   u   排序时间u   排序完成，最高分: r   r;   u   ，最低分: �����u   排序失败: u   排序失败u   数据排序时出错:
N)r
   r   r    �sortedr>   r   r\   r]   r^   rk   r!   r"   r   r#   )r   r%   rp   �sorted_datarD   r&   r)   r   r   r   �sort_by_blue_ocean_score  s&   ����z%DataImporter.sort_by_blue_ocean_scorec                 C   sz  �z|sW dS t |�}i }d}i }|D ],}|�dd�}|�|d�d ||< |�dd�}||7 }|�dd�}	|�|	d�d ||	< q|dkrH|| nd}
d	d
� d|� d|
d
�d|d �dd�� d|d �dd�� d|d �dd�� d|d �dd�� d�}t|�� dd� dd�D ]\}}|| d }
|d|� d|� d|
d
�d�7 }q�|d7 }t|�� dd� dd�D ]\}	}|| d }
|d|	� d|� d|
d
�d�7 }q�|d7 }t|d d!� �D ]2\}}|d"k r�g d#�| n|d � d$�}|d|� d%|�dd�� d|�dd�� d&|�dd�� d'�	7 }q�|d!k�r|d(|d! � d)�7 }|W S  t�y< } z| j�d*|� �� d+t|�� �W  Y d }~S d }~ww ),u   生成批量分析报告u   ❌ 没有数据可分析r   r<   �   未知r8   r;   rR   u   未知来源u   📊 批量导入分析报告
z2==================================================u%   

📈 总体统计:
  总产品数: u    个
  平均蓝海指数: z.1fu    分
  最高分产品: ri   rd   u    分)
  最低分产品: rw   u    分)

🏆 评级分布:
c                 S   �   | d S �Nr8   r   rr   r   r   r   rt   Z  �    z=DataImporter.generate_batch_analysis_report.<locals>.<lambda>Tru   �d   �  rN   u    个 (z%)
u   
📁 数据来源分布:
c                 S   r|   r}   r   rr   r   r   r   rt   _  r~   u(   
🥇 蓝海指数排行榜 (前10名):
Nr9   �   )u   🥇u   🥈u   🥉�.rc   u    分 (�)
�
     ... 还有 �    个产品
u    生成批量分析报告失败: u   ❌ 生成报告失败: )	r    rk   rx   r   r>   r!   r
   r"   rB   )r   r%   �total_count�rating_statsZ	score_sumZsource_statsr&   �ratingZscore�source�	avg_score�report�count�
percentagerD   Z	rank_iconr)   r   r   r   �generate_batch_analysis_report.  sb   �������  "<
��z+DataImporter.generate_batch_analysis_report�old_data�new_datac              
   C   s�  z�dd� t |�D �}dd� t |�D �}t|�t|�dddd�g g g d�}t|�� �}t|�� �}||@ }|| }	|| }
t|�|d d< t|	�|d d	< t|
�|d d
< |D ]4}|| }|| }
|�dd�}|
�dd�}||kr�||||| |�dd
�|
�dd
�d�}|d �|� qV|	D ]}|d �||| �dd�|| �dd
�d�� q�|
D ]}|d �||| �dd�|| �dd
�d�� q�|W S  ty� } z| j�d|� �� dt	|�iW  Y d}~S d}~ww )u   对比两次数据的变化c                 S   �$   i | ]\}}|� d d|� ��|�qS �ri   u   产品_rq   ��.0rD   r&   r   r   r   �
<dictcomp>w  �   $ z5DataImporter.compare_data_changes.<locals>.<dictcomp>c                 S   r�   r�   rq   r�   r   r   r   r�   x  r�   r   )�   第一次数据量�   第二次数据量�   共同产品数�   新增产品数�   消失产品数)�   总体统计�   评分变化�   新增产品�   消失产品r�   r�   r�   r�   r;   r<   r{   )ri   �	   原评分�	   新评分�   变化u	   原评级u	   新评级r�   r�   )ri   r;   r<   r�   u   对比数据变化失败: �   错误N)
r>   r    �set�keysrk   r   r!   r
   r"   rB   )r   r�   r�   Zold_mapZnew_map�
comparison�	old_names�	new_namesZcommon_namesZnew_names_onlyZold_names_only�nameZold_itemZnew_itemZ	old_scoreZ	new_score�changer)   r   r   r   �compare_data_changesr  sl   ��

��
�
���z!DataImporter.compare_data_changesr�   c           
   
   C   sp  d|v rd|d � �S d}|d7 }|d }|d7 }|d|d � d	�7 }|d
|d � d	�7 }|d|d
 � d�7 }|d|d � d�7 }|d|d � d�7 }|d }|r�|dt |�� d�7 }|dd� D ])}|d dkrkdnd}|d|� d|d � d |d! � d"|d# � d$|d d%�d&�7 }qat |�dkr�|d't |�d � d(�7 }n|d)7 }|d*7 }|d+ }|r�|d,t |�� d-�7 }|dd.� D ]}|d/|d � d |d0 � d1|d2 � d&�7 }q�t |�d.kr�|d't |�d. � d3�7 }n|d47 }|d*7 }|d5 }	|	�r2|d6t |	�� d-�7 }|	dd.� D ]}|d/|d � d |d0 � d1|d2 � d&�7 }�qt |	�d.k�r0|d't |	�d. � d7�7 }|S |d87 }|S )9u   生成对比报告r�   u   ❌ 对比分析失败: u   📊 数据对比分析报告
z4==================================================

r�   u   📈 总体统计:
u     第一次数据: r�   r�   u     第二次数据: r�   u     共同产品: r�   u    个
u     新增产品: r�   u     消失产品: r�   u    个

r�   u   🔄 评分变化 (u
    个产品):
Nr9   r�   r   u   📈u   📉r�   rc   ri   rN   r�   u    → r�   rd   z+.1fr�   r�   u    个产品有变化
u   🔄 评分变化: 无变化
rf   r�   u   🆕 新增产品 (u    个):
�   u     • r;   u   分 (r<   u    个新产品
u   🆕 新增产品: 无
r�   u   ❌ 消失产品 (u    个消失的产品
u   ❌ 消失产品: 无
)r    )
r   r�   r�   Zstats�changesr�   �	directionZnew_products�productZdisappearedr   r   r   �generate_comparison_report�  sT   <�*�,�z'DataImporter.generate_comparison_reportN)ri   )F)r   �
__module__�__qualname__�__doc__r   rB   r   r   r   r   r*   r7   rG   ra   r`   ro   �boolrz   r�   r�   r�   r   r   r   r   r      s6    ""*&(T��
�/��
�D�

�Jr   )r�   rW   r0   �pandasr   r
   �typingr   r   r   r   �tkinterr   r   r   r   r   r   �<module>   s   